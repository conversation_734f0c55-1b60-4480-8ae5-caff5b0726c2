#ifndef DATA_TYPE_H
#define DATA_TYPE_H
#pragma pack(push)
#pragma pack(4)

typedef struct Tag_Position
{
	float x;
	float y;
	float z;
	unsigned long long timeStamp; //ns
}TagPosition;

typedef struct Tag_Point
{
  float x;
  float y;
  float z;
  float nx;
  float ny;
  float nz;
  float intensity;
  unsigned char r;
  unsigned char g;
  unsigned char b;
  unsigned char a;
  unsigned char label;
  unsigned long long timeStamp; //ns
} TagPoint;

typedef struct Tag_PointShort
{
  float x;
  float y;
  float z;
  unsigned char intensity;
  unsigned char r;
  unsigned char g;
  unsigned char b;
}TagPointShort;

typedef struct Tag_PointShortI
{
  unsigned short int x;
  unsigned short int y;
  unsigned short int z;
  unsigned char intensity;
  unsigned char r;
  unsigned char g;
  unsigned char b;
}Tag_PointShortI;

typedef struct Tag_PointCloud
{
  TagPoint* data{};
  unsigned int size{0};
} TagPointCloud;

typedef struct Tag_Triangle
{
	unsigned int index0{0};
	unsigned int index1{0};
	unsigned int index2{0};
}TagTriangle;

typedef struct Tag_TriangleFacet
{
  TagTriangle* data;
  unsigned int size{0};
}TagTriangleFacet;

typedef struct Tag_RgbImage
{
	unsigned int width{0};
	unsigned int height{0};
	unsigned long long timeStamp{0}; //ns
	unsigned char* data; //r g b 24bit bytes_length = width*height*3
}TagRgbImage;

typedef struct Tag_DepthImage
{
	unsigned int width{0};
	unsigned int height{0};
	unsigned long long timeStamp{0};  // ns
	float* data{};                    // 32bit   bytes_length = width*height
} TagDepthImage;

typedef struct Tag_SkeletonPoint
{
  int point_cnt{0};    // number of 3d points
  float points[96]{};  // up to 32 3d points (32*3=96)
} TagSkeletonPoint;

typedef struct Tag_DeviceEvent
{
  unsigned char event_type{0};
  unsigned int uuid_size{0};
  char uuid[128];
}TagDeviceEvent;

typedef struct Tag_CString
{
	unsigned int length{0};
	char data[128];
}TagCString;

typedef struct Tag_CString2
{
	unsigned int length{0};
	char data[2048];
}Tag_CString2;

typedef struct Tag_TConfig
{
  TagCString topic;
  TagCString type;
  TagCString process_types[32];
  unsigned int process_types_size{0};
}TagTConfig;

typedef struct Tag_TSetting
{
  TagCString topic;
  TagCString compressedType;
}TagTSetting;

typedef struct Tag_TVersion
{
  TagCString version;
  TagCString commit_id;
}TagTVersion;

typedef struct Tag_SlamStatus 
{
  bool is_valid     = false; 
  bool is_src_lidar = false;    // 是否为Lidar 更新
  bool is_src_image = false;    // 是否为Image 更新
  bool is_degenerate = false;   // 是否退化
  double degenerate_score = 0;  // 退化分数
  bool is_image_degenerate = false;   // 是否退化
  double image_degenerate_score = 0;  // 退化分数
  bool   is_enable_relocalization = false; 
  int    cur_map_id      = -1;  // 地图分段id 
}TagSlamStatus; 

#pragma pack(pop)

#endif //DATA_TYPE_H