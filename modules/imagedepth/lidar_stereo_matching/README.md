# 1. 说明

本模块需要运行在nvidia显卡上，运行平台需预先安装驱动及cuda。

# 2. 添加cuda环境变量

```
nano ~/.bashrc
```

文件末尾添加以下内容，cuda路径为运行平台中安装的cuda路径
```
export PATH="/usr/local/cuda/bin:$PATH"
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"
export CUDA_HOME="/usr/local/cuda"
```
保存后运行
```
source ~/.bashrc
```

检验环境变量
```
nvcc --version
```

# 3. 编译运行指令
```sh
mkdir build && cd build && cmake .. && make # 编译
./offline_sample ../example/data/ ../config # 运行示例程序
```

# 4. 示例工程
offline_sample.cpp中给出了接口调用方式的示例，主函数中展示了算法初始化和数据推送过程，回调函数中展示了算法结果的解析过程。对此有以下说明：
1. 算法输入数据需要时间戳同步
2. 算法输出的深度图单位是m，数据类型是float，深度图数据结构中的bits_size=32
3. 算法返回的图像都已经去除了畸变
4. 输入数据帧率高于算法帧率将会导致结果丢帧
5. result_handler中可以对算法结果进行显示，如果显示点云可以去掉对应代码块的注释，默认只显示图像
6. 图像显示窗口按's'键将在当前目录下保存二进制深度图数据、pcd格式彩色稠密点云数据、二进制彩色稠密点云数据


    二进制点云数据存储格式：

    | point_num| point_step| timestamp| buffer size|point_cloud_buffer|
    | :---: | :---: | :--: | :---: | :---: | 
    | 4 Bytes | 4 Bytes| 8 Bytes| 4 Bytes |buffer size Bytes|
7. 输出的彩色稠密点云在Lidar坐标系下