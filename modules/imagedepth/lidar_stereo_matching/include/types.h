#pragma once
#include <cstdint>
#include <Eigen/Dense>
#include <opencv2/opencv.hpp>
namespace robosense {
namespace listereo
{
#define SIMD_WIDTH 16
typedef uint16_t CostType;
typedef int16_t DispType;

static constexpr unsigned int WARP_SIZE = 32u;
static constexpr uint16_t INVALID_DISP = 0;

enum RUNNING_MODE{
  RUNNING_IN_CPU,
  RUNNING_IN_GPU_OCL,
  RUNNING_IN_GPU_CUDA
};

struct LiSGMParam
{
  int min_disp_ = 0;
  int num_disp_ = 256; //256;
  int r_h_sad_ = 4; // [px]
  int r_w_sad_ = 4; // [px]
  int r_prior_ = 10; // [px]
  int r_triangle_ = 30; // [px]
  int r_3d_max_ = 2500; // [mm]
  int r_3d_min_ = 0; // [mm]
  int r_3d_k_ = 10;
  int k_diff_ = 12; // [gray]
  double thres_angle_ = 85.; // [deg]
  uint16_t max_pg_[2] = {128, 128};
  uint16_t p1_= 212;
  uint16_t p2_= 1250;
  uint16_t pd1_= 102;
  uint16_t pd2_= 3290;
  uint16_t r_pd_ = 3; // [px]
  uint16_t weight_gray_ = 1;
  uint16_t weight_grad_ = 2;
  uint16_t weight_depth_ = 8;
  uint16_t weight_shift_ = 4;
  uint16_t sub_shift_ = 6;
  uint16_t max_cost_ = 0x7fff;
  uint16_t scale_vis_ = 1;

  int uniqueness_ratio_ = 10; // [%]
  int disp_lr_max_diff_ = 1; // [px]
  int max_speckle_size_ = 200; // [px]
  int speckle_diff_px_ = 1; // [px]
  int speckle_diff_mm_ = 200; // [mm]

  double clip_limit_ = 4.0;
  cv::Size tile_grid_size_ = cv::Size(8, 8);

  float thres_cross_check_ = 1.; // [px]
  float semi_rel_error_ = 0.02;

  Eigen::Matrix3d leftCamK_ = Eigen::Matrix3d::Identity();
  Eigen::Matrix<double, 8, 1> leftCamD_;
  Eigen::Matrix3d rightCamK_ = Eigen::Matrix3d::Identity();
  Eigen::Matrix<double, 8, 1> rightCamD_;

  Eigen::Matrix4d T_leftcam_rightcam_ = Eigen::Matrix4d::Identity();
  Eigen::Matrix4d T_lidar_leftcam_ = Eigen::Matrix4d::Identity();
  Eigen::Matrix4d T_rightcam_lidar_ = Eigen::Matrix4d::Identity(); 

  double baseline_ = 529.50e-3;
  double focal_length_ = 1734.04;
  double doffs_ = 0.;
  double speckle_ratio_ = 0.;

  // params for opencl and cuda
  RUNNING_MODE running_mode = RUNNING_MODE::RUNNING_IN_GPU_CUDA;
  int platform_idx = 0;
  int device_idx = 0;
  int img_width = 1600;
  int img_height = 1200;
  int depth_type = 16;
  int enable_lidar = 1;
  int enable_vision = 0;
  int path_num = 4;
  float depth_scale = 1.0f;

  float dist_jitter_thresh = 0.05f;
  int thread_priority = 99;

  LiSGMParam() = default;
  ~LiSGMParam() = default;
};
} // namespace listereo
} // namespace robosense