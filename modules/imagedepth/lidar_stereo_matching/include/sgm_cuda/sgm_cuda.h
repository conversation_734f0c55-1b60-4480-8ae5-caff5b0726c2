#pragma once
#include <cstdint>
#include <Eigen/Dense>
#include <opencv2/opencv.hpp>
#include "types.h"
#include "sgm_cuda/device_image.h"
#include "sgm_cuda/device_data.h"
#include "sgm_cuda/cuda_kernels_api.h"
namespace robosense {
namespace listereo {
  enum class CudaImageType{
    UNDISTORT_IMAGE_LEFT,
    UNDISTORT_IMAGE_RIGHT,
    RET_DISP,
    RET_DEPTH,
    PRIOR_DEPTH,
    PRIOR_DISP,
    LIDAR_DENSE_DEPTH,
  };
  class StereoSGMCuda
  {    

  public: 
    StereoSGMCuda() = default;
    StereoSGMCuda(
      const listereo::LiSGMParam &params, 
      const cv::Mat &map_x_l, 
      const cv::Mat &map_y_l, 
      const cv::Mat &map_x_r, 
      const cv::Mat &map_y_r,
      const int color_point_type_size = 0);
    ~StereoSGMCuda()=default;

    int run_lidar(
      const std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> &uv_p3d, 
      const std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &triangles, 
      const cv::Mat &idx_mat,
      const std::vector<Eigen::Vector3f> &points3d
    );
    int run_vision(
      uchar* left_buffer, 
      uchar* right_buffer,
      int channel = 3      
    );
    int get_cuda_image(cv::Mat &ret, CudaImageType image_type);
    int get_cuda_data(std::shared_ptr<std::vector<uint8_t>> &host_data, uint32_t *valid_point_num);

  private: 
    int CvtColor2Gray(); 
    int ImageUndistort();
    int ComputeSobelCuda();
    int ComputeCensusCuda(int img_idx);  
    int ComputeVisionCostAndAggCuda();
    int ComputeDisparityFromAggCuda();
    int MedianFilterCuda();
    int LRConsistencyCheckCuda();
    int Disp2DepthCuda();
    int Depth2DispCuda();
    int ComputePriorDepthCuda(int point_num, int triangle_num);
    int ComputeAverageNormalsCuda(int point_num);
    int FilterBadPriorDepthCuda(int point_num);

    int ProjectPCL2Img(int points_num);
    int GetDensePriorDepth();
    int GetColorPointCloud(int *valid_point_count);

    int mi_dst_pitch_ = -1;
    int mi_src_pitch_ = -1;

    float mf_ks_left_[4] = {1.0f, 1.0f, 0.0f, 0.0f}; 
    float mf_ks_right_[4] = {1.0f, 1.0f, 0.0f, 0.0f};
    float mf_dist_left_[8] = {0.0f};
    float mf_dist_right_[8] = {0.0f};

    float mf_thres_cos_ = std::cos(85.0f/180.*M_PI);
    int mi_color_point_type_size_;
    int mi_valid_points_count;

    listereo::LiSGMParam m_params_;

    DeviceImage m_d_src_gray_[2];
    DeviceImage m_d_src_gray_undist_[2];
    DeviceImage m_d_src_color_[2];
    DeviceImage m_d_src_color_undist_[2];
    DeviceImage m_d_map_x_[2];
    DeviceImage m_d_map_y_[2];
    DeviceImage m_d_sobel_gx_[2];
	  DeviceImage m_d_census_[2];
    DeviceImage m_d_gx_census_[2];
    DeviceImage m_d_cost_;
    DeviceImage m_d_tmp_[2];
    DeviceImage m_d_disp_[2];
    DeviceImage m_d_depth_;
    
    DeviceData m_d_uv_p3d_;
    DeviceData m_d_triangles_;
    DeviceImage m_d_idx_mat_;
    DeviceData m_d_normals_;
    DeviceData m_d_normals_fixed_;
    DeviceData m_d_normals_count_;
    DeviceImage m_d_prior_disp_;
    DeviceData m_d_ks_[2];
    DeviceData m_d_dist_[2];
    DeviceData m_d_T_;

    DeviceData m_d_point3d_;
    DeviceImage m_d_sparse_depth_;
    DeviceImage m_d_dense_depth_;
    DeviceData m_d_color_point3d_;

  };
} // namespace listereo
} // namespace robosense