
#pragma once
#include "sgm_cuda/device_image.h"
#include "sgm_cuda/device_data.h"
#include <cstdint>
namespace robosense {
namespace listereo
{
namespace cuda_kernels_api
{
    // 图像畸变矫正（没有极线矫正）
    int image_undistort(
        const DeviceImage& src_left, 
        const DeviceImage& src_right,
        DeviceImage& dst_left, 
        DeviceImage& dst_right,
        const DeviceData& K_left, 
        const DeviceData& K_right,
        const DeviceData& dist_left,
        const DeviceData& dist_right);
    // 利用映射图进行畸变矫正，映射图来自opencv极线矫正和去畸变
    int remap(DeviceImage& dst, const DeviceImage& src, const DeviceImage& map_x, const DeviceImage& map_y);

    int color2gray(const DeviceImage& src_left, const DeviceImage& src_right, DeviceImage& dst_left, DeviceImage& dst_right);

    int census_transform(const DeviceImage& src, DeviceImage& dst);

    int sobel_gx_feature(const DeviceImage& src_left, const DeviceImage& src_right, DeviceImage& dst_left, DeviceImage& dst_right);

    int cost_aggregation(
        const DeviceImage& srcL, 
        const DeviceImage& srcR, 
        DeviceImage& dst, 
        unsigned short P1, 
        unsigned short P2, 
        int path_num, 
        int min_disp, 
        int max_disp);

    int disp_from_agg(
        const DeviceImage& src, 
        const DeviceImage& src_prior_disp, 
        DeviceImage& dstL, 
        DeviceImage& dstR,
        float uniqueness,
        int min_disp, 	
        int max_disp,
        int path_num,
        unsigned short sub_pixel_shift,
        unsigned short r_pd,
        unsigned short pd1,
        unsigned short pd2,
        float alpha,
        float beta);

    int median_filter(const DeviceImage& src, DeviceImage& dst);

    int check_consistency(
        DeviceImage& dispL, 
        const DeviceImage& dispR, 
        const DeviceImage& srcL, 
        int min_disp, 
        int LR_max_diff, 
        int sub_pixel_shift);

    int vision_disp2depth(
        const DeviceImage& disp, 
        DeviceImage& depth, 
        float baseline, 
        float focal_length,
        float doffs,
        float sub_pixel_scale);
    
    int disp2depth(
        const DeviceImage& disp, 
        const DeviceImage& prior_depth, 
        DeviceImage& depth, 
        float baseline, 
        float focal_length,
        float doffs,
        float sub_pixel_scale,
        int enable_lidar, 
        float semi_rel_error,
        float thres_cross_check);

    int depth2disp(
        const DeviceImage& depth, 
        DeviceImage& disp, 
        float baseline, 
        float focal_length,
        float doffs,
        float sub_pixel_scale);
    
    int triangle2depth(
        const DeviceData& uv_p3d, 
        const DeviceData& triangles, 
        const DeviceImage& idx_mat, 
        DeviceImage& depth, 
        DeviceData& normals,
        DeviceData& normals_count,
        const int point_num,
        const int triangle_num,
        const float thres_cos,
        const DeviceData& K);

    int rectify_normals(
        const DeviceData& src_normals,
        const DeviceData& src_normals_count,
        DeviceData& dst_normals_fixed,
        const int point_num);

    int filter_bad_prior_depth(
        DeviceImage& depth,
        const DeviceData& uv_p3d,
        const DeviceData& normals_fixed,
        const DeviceImage& idx_mat,
        const int point_num,
        const int r,
        const DeviceData& K);

    int project_pcl2img(
        const DeviceData& points3d,  
        DeviceImage& p2d_img,
        const int point_num,
        const DeviceData& K
        );

    int get_dense_depth(
        const DeviceImage& sparse_prior_depth,
        DeviceImage& dense_prior_depth,
        const int search_radius,
        const float distance_jitter_thresh,
        const DeviceData& K
    );

    int get_color_pointcloud(
        const DeviceImage& dense_depth,
        const DeviceImage& color_image,
        DeviceData& color_points3d, 
        const DeviceData& K,
        const DeviceData& T,
        int *valid_points_count
    );
} // namespace cuda_kernels_api
} // namespace listereo
} // namespace robosense

