  min_disp: 0
  max_disp: 256
  r_h_sad: 4 # [px]
  r_w_sad: 4 # [px]
  r_prior: 10 # [px]
  r_triangle: 30 # [px]
  r_3d_max: 2500 # [mm]
  r_3d_min: 0 #[mm]
  r_3d_k: 10 
  k_diff: 12 #[gray]
  thres_angle: 80. #[deg]
  max_pg: [128, 128]
  p1: 212
  p2: 1250
  pd1: 102
  pd2: 3290
  r_pd: 3
  weight_gray: 1
  weight_grad: 2
  weight_depth: 8
  weight_shift: 4
  sub_shift: 6
  max_cost: 0x7fff
  scale_vis: 1

  uniqueness_ratio: 10
  disp_lr_max_diff: 1
  max_speckle_size: 200
  speckle_diff_px: 1
  speckle_diff_mm: 200

  clip_limit: 4.0
  tile_grid_size: [8, 8]

  thres_cross_check: 1. # [px]
  semi_rel_error: 0.02

  baseline: 529.50e-3
  focal_length: 1734.04
  doffs: 0
  speckle_ratio: 0.0004
  
  # Left camera intrinsic parameters
  leftCamK: [5.9835454137135764e+02, 6.0088706056233741e+02, 8.2581011721158620e+02, 5.8102740035566785e+02]
  leftCamD: [-4.0832594003277461e-01, 1.6086803429189169e+00,
             -7.4513250511887828e-05, -1.0775128318085694e-04,
             3.4595407372731224e-01, -3.5001896900023638e-01,
             1.4828870459016457e+00, 4.7086437629215477e-01]
  # Right camera intrinsic parameters
  rightCamK: [6.0093091689726339e+02, 6.0354023165864885e+02, 8.1106771286416779e+02, 6.0912271257870646e+02]
  rightCamD: [-3.6423402662863223e-02, 3.2763739730759616e+00,
             2.7879443099586504e-04, -4.3959068333542547e-04,
             -1.3594487807600086e-01, 2.9424023143799754e-02,
             3.1315297898820802e+00, 3.0901574656470837e-02]

  img_width: 1600
  img_height: 1200

  # camera extrinsic parameters
  T_leftcam_rightcam:
    translation: [-6.6291797175827408e-02, -1.0557277373576330e-04, 8.4683062587681763e-04]
    rotation: [1.64679467e-03, 3.40991470e-03, 8.92383984e-05, 9.99992826e-01]

  # lidar extrinsic parameters
  T_lidar_leftcam:
    translation: [0.03, 0.06, 0.02]
    rotation: [-0.0392526, 0.0191826, 0.000753688, 0.999045]
  
  T_rightcam_lidar:
    translation: [0.0, 0.0, 0.0]
    rotation: [0.0, 0.0, 0.0, 1.0]

  # params for opencl and cuda
  running_mode: "CUDA" # 0: CUP; 1: CUDA 2: OpenCL;
  platform_idx: 0
  device_idx: 0
  depth_type: 16
  enable_lidar: 1
  enable_vision: 0
  path_num: 4
  thread_priority: 50

  dist_jitter_thresh: 0.04