#include "depth_estimation_interface.h"
#include "types.h"
#include "sgm_cpu/sgm_cpu.h"
#include <iostream>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <stdexcept>
#include <yaml-cpp/yaml.h>


#include <pcl/point_types.h>
#include <pcl/io/pcd_io.h>

typedef pcl::PointXYZINormal PointType;
typedef pcl::PointCloud<PointType> PointCloudXYZI;
typedef PointCloudXYZI::Ptr CloudPtr;

#if ALG_RUNNING_MODE == 1
#include "sgm_cuda/sgm_cuda.h"
#elif ALG_RUNNING_MODE == 2
#include "sgm_ocl/sgm_ocl.h"
void context_error_callback(const char* errinfo, const void* private_info, size_t cb, void* user_data)
{
    std::cout << "opencl error : " << errinfo << std::endl;
}
#endif


namespace robosense {
namespace listereo {

// 数据包，用于在线程间传递数据
struct SensorDataPacket {
    LidarData lidar_data;
    StereoImage image_data;
};

class DepthEstimationImpl : public DepthEstimationInterface {
public:
    DepthEstimationImpl() {
        std::cout << "DepthEstimationImpl: Instance created." << std::endl;
    }

    ~DepthEstimationImpl() override {
        std::cout << "DepthEstimationImpl: Instance destroyed." << std::endl;
        // 在析构函数中停止线程，确保安全退出
        stopWorker();
    }

    /**
     * @brief 初始化算法，读取配置文件。
     * @param config_file_path 配置文件路径。
     * @return true 初始化成功；false 初始化失败。
     */
    bool initialize(const std::string& config_file_path) {
        if (m_is_initialized) {
            std::cout << "DepthEstimationImpl: Already initialized." << std::endl;
            return true;
        }

        std::cout << "DepthEstimationImpl: Initializing with config file: " << config_file_path << std::endl;
        m_config_path = config_file_path;

        // 检查配置文件是否存在
        std::ifstream file(config_file_path);
        if (!file.good()) {
            std::cerr << "DepthEstimationImpl: Error - Config file not found: " << config_file_path << std::endl;
            return false;
        }
        file.close();

        // 读取配置文件
        YAML::Node config;
        try {
            config = YAML::LoadFile(config_file_path);
        } catch (const YAML::Exception& e) {
            std::cerr << "DepthEstimationImpl: Error - Failed to parse config file: " << e.what() << std::endl;
            return false;
        }

        // 解析配置参数
        try {            
            m_param.min_disp_ = config["min_disp"] ? config["min_disp"].as<int>() : m_param.min_disp_;
            m_param.num_disp_ = config["max_disp"] ? config["max_disp"].as<int>() : m_param.num_disp_;                     
            m_param.r_h_sad_ = config["r_h_sad"] ? config["r_h_sad"].as<int>() : m_param.r_h_sad_;
            m_param.r_w_sad_ = config["r_w_sad"] ? config["r_w_sad"].as<int>() : m_param.r_w_sad_;
            m_param.r_prior_ = config["r_prior"] ? config["r_prior"].as<int>() : m_param.r_prior_;
            m_param.r_triangle_ = config["r_triangle"] ? config["r_triangle"].as<int>() : m_param.r_triangle_;
            m_param.r_3d_max_ = config["r_3d_max"] ? config["r_3d_max"].as<int>() : m_param.r_3d_max_;
            m_param.r_3d_min_ = config["r_3d_min"] ? config["r_3d_min"].as<int>() : m_param.r_3d_min_;
            m_param.r_3d_k_ = config["r_3d_k"] ? config["r_3d_k"].as<int>() : m_param.r_3d_k_;
            m_param.k_diff_ = config["k_diff"] ? config["k_diff"].as<int>() : m_param.k_diff_;
            m_param.thres_angle_ = config["thres_angle"] ? config["thres_angle"].as<double>() : m_param.thres_angle_;
            if (config["max_pg"] && config["max_pg"].IsSequence() && config["max_pg"].size() == 2) {
                m_param.max_pg_[0] = config["max_pg"][0].as<uint16_t>();
                m_param.max_pg_[1] = config["max_pg"][1].as<uint16_t>();
            }
            m_param.p1_ = config["p1"] ? config["p1"].as<uint16_t>() : m_param.p1_;
            m_param.p2_ = config["p2"] ? config["p2"].as<uint16_t>() : m_param.p2_;   
            m_param.pd1_ = config["pd1"] ? config["pd1"].as<uint16_t>() : m_param.pd1_;
            m_param.pd2_ = config["pd2"] ? config["pd2"].as<uint16_t>() : m_param.pd2_;
            m_param.r_pd_ = config["r_pd"] ? config["r_pd"].as<uint16_t>() : m_param.r_pd_;
            m_param.weight_gray_ = config["weight_gray"] ? config["weight_gray"].as<uint16_t>() : m_param.weight_gray_;
            m_param.weight_grad_ = config["weight_grad"] ? config["weight_grad"].as<uint16_t>() : m_param.weight_grad_;
            m_param.weight_depth_ = config["weight_depth"] ? config["weight_depth"].as<uint16_t>() : m_param.weight_depth_;
            m_param.weight_shift_ = config["weight_shift"] ? config["weight_shift"].as<uint16_t>() : m_param.weight_shift_;            
            m_param.sub_shift_ = config["sub_shift"] ? config["sub_shift"].as<int>() : m_param.sub_shift_;
            m_param.max_cost_ = config["max_cost"] ? config["max_cost"].as<uint16_t>() : m_param.max_cost_;
            m_param.scale_vis_ = config["scale_vis"] ? config["scale_vis"].as<uint16_t>() : m_param.scale_vis_;
            
            m_param.uniqueness_ratio_ = config["uniqueness_ratio"] ? config["uniqueness_ratio"].as<int>() : m_param.uniqueness_ratio_;
            m_param.disp_lr_max_diff_ = config["disp_lr_max_diff"] ? config["disp_lr_max_diff"].as<int>() : m_param.disp_lr_max_diff_;
            m_param.max_speckle_size_ = config["max_speckle_size"] ? config["max_speckle_size"].as<int>() : m_param.max_speckle_size_;
            m_param.speckle_diff_px_ = config["speckle_diff_px"] ? config["speckle_diff_px"].as<int>() : m_param.speckle_diff_px_;
            m_param.speckle_diff_mm_ = config["speckle_diff_mm"] ? config["speckle_diff_mm"].as<int>() : m_param.speckle_diff_mm_;
            
            m_param.clip_limit_ = config["clip_limit"] ? config["clip_limit"].as<double>() : m_param.clip_limit_;
            if (config["tile_grid_size"] && config["tile_grid_size"].IsSequence() && config["tile_grid_size"].size() == 2) {
                m_param.tile_grid_size_.width = config["tile_grid_size"][0].as<int>();
                m_param.tile_grid_size_.height = config["tile_grid_size"][1].as<int>();
            }
            
            m_param.thres_cross_check_ = config["thres_cross_check"] ? config["thres_cross_check"].as<float>() : m_param.thres_cross_check_;
            m_param.semi_rel_error_ = config["semi_rel_error"] ? config["semi_rel_error"].as<float>() : m_param.semi_rel_error_;
            m_param.baseline_ =  config["baseline"] ? config["baseline"].as<double>() : m_param.baseline_;
            m_param.focal_length_ =  config["focal_length"] ? config["focal_length"].as<double>() : m_param.focal_length_;
            m_param.doffs_ =  config["doffs"] ? config["doffs"].as<double>() : m_param.doffs_;
            m_param.speckle_ratio_ =  config["speckle_ratio"] ? config["speckle_ratio"].as<double>() : m_param.speckle_ratio_;

            Eigen::Matrix3d cam_mat_l = Eigen::Matrix3d::Identity();
            if (config["leftCamK"] && config["leftCamK"].IsSequence() && config["leftCamK"].size() == 4) {
                cam_mat_l(0, 0) = config["leftCamK"][0].as<double>(); // fx
                cam_mat_l(1, 1) = config["leftCamK"][1].as<double>(); // fy
                cam_mat_l(0, 2) = config["leftCamK"][2].as<double>(); // cx
                cam_mat_l(1, 2) = config["leftCamK"][3].as<double>(); // cy
            }

            Eigen::Matrix3d cam_mat_r = Eigen::Matrix3d::Identity();
            if (config["rightCamK"] && config["rightCamK"].IsSequence() && config["rightCamK"].size() == 4) {
                cam_mat_r(0, 0) = config["rightCamK"][0].as<double>(); // fx
                cam_mat_r(1, 1) = config["rightCamK"][1].as<double>(); // fy
                cam_mat_r(0, 2) = config["rightCamK"][2].as<double>(); // cx
                cam_mat_r(1, 2) = config["rightCamK"][3].as<double>(); // cy
            }

            Eigen::Matrix<double, 8, 1> cam_dist_l;
            if (config["leftCamD"] && config["leftCamD"].IsSequence() && config["leftCamD"].size() == 8) {
                for(int i = 0; i < 8; i++){
                    cam_dist_l(i, 0) = config["leftCamD"][i].as<double>();
                }
            }

            Eigen::Matrix<double, 8, 1> cam_dist_r;
            if (config["rightCamD"] && config["rightCamD"].IsSequence() && config["rightCamD"].size() == 8) {
                for(int i = 0; i < 8; i++){
                    cam_dist_r(i, 0) = config["rightCamD"][i].as<double>(); 
                }
            }
            
            if (config["T_leftcam_rightcam"]["translation"] && config["T_leftcam_rightcam"]["translation"].IsSequence() && config["T_leftcam_rightcam"]["translation"].size() == 3) {
                for(int i = 0; i < 3; i++){
                    m_param.T_leftcam_rightcam_(i, 3) = config["T_leftcam_rightcam"]["translation"][i].as<double>();
                }
            }
            Eigen::Quaterniond quat_leftcam_rightcam;
            if (config["T_leftcam_rightcam"]["rotation"] && config["T_leftcam_rightcam"]["rotation"].IsSequence() && config["T_leftcam_rightcam"]["rotation"].size() == 4) {
                quat_leftcam_rightcam.x() = config["T_leftcam_rightcam"]["rotation"][0].as<double>();
                quat_leftcam_rightcam.y() = config["T_leftcam_rightcam"]["rotation"][1].as<double>();
                quat_leftcam_rightcam.z() = config["T_leftcam_rightcam"]["rotation"][2].as<double>();
                quat_leftcam_rightcam.w() = config["T_leftcam_rightcam"]["rotation"][3].as<double>();
                m_param.T_leftcam_rightcam_.block<3, 3>(0, 0) = quat_leftcam_rightcam.toRotationMatrix();                
            }

            Eigen::Quaterniond quat_lidar_leftcam;
            if (config["T_lidar_leftcam"]["rotation"] && config["T_lidar_leftcam"]["rotation"].IsSequence() && config["T_leftcam_rightcam"]["rotation"].size() == 4) {
                quat_lidar_leftcam.x() = config["T_lidar_leftcam"]["rotation"][0].as<double>();
                quat_lidar_leftcam.y() = config["T_lidar_leftcam"]["rotation"][1].as<double>();
                quat_lidar_leftcam.z() = config["T_lidar_leftcam"]["rotation"][2].as<double>();
                quat_lidar_leftcam.w() = config["T_lidar_leftcam"]["rotation"][3].as<double>();
                m_param.T_lidar_leftcam_.block<3, 3>(0, 0) = quat_lidar_leftcam.toRotationMatrix();                
            }
            if (config["T_lidar_leftcam"]["translation"] && config["T_lidar_leftcam"]["translation"].IsSequence() && config["T_lidar_leftcam"]["translation"].size() == 3) {
                for(int i = 0; i < 3; i++){
                    m_param.T_lidar_leftcam_(i, 3) = config["T_lidar_leftcam"]["translation"][i].as<double>();
                }
            }

            //std::cout << "m_param.T_leftcam_rightcam_ " << m_param.T_leftcam_rightcam_ << std::endl;            

            if (config["running_mode"]) {
                std::string mode = config["running_mode"].as<std::string>();
                if (mode == "CPU") {
                    m_param.running_mode = RUNNING_MODE::RUNNING_IN_CPU;
                } else if (mode == "CUDA") {
                    m_param.running_mode = RUNNING_MODE::RUNNING_IN_GPU_CUDA;
                } else if (mode == "OpenCL") {       
                    m_param.running_mode = RUNNING_MODE::RUNNING_IN_GPU_OCL;
                } else {
                    std::cerr << "DepthEstimationImpl: Warning - Unknown running_mode in config, defaulting to CPU." << std::endl;
                    m_param.running_mode = RUNNING_MODE::RUNNING_IN_CPU;
                }
            }
            m_param.platform_idx = config["platform_idx"] ? config["platform_idx"].as<int>() : m_param.platform_idx;
            m_param.device_idx = config["device_idx"] ? config["device_idx"].as<int>() : m_param.device_idx;
            m_param.img_width = config["img_width"] ? config["img_width"].as<int>() : m_param.img_width;
            m_param.img_height = config["img_height"] ? config["img_height"].as<int>() : m_param.img_height;
            m_param.depth_type = config["depth_type"] ? config["depth_type"].as<int>() : m_param.depth_type;
            m_param.enable_lidar = config["enable_lidar"] ? config["enable_lidar"].as<int>() : m_param.enable_lidar;
            m_param.enable_vision = config["enable_vision"] ? config["enable_vision"].as<int>() : m_param.enable_vision;
            m_param.path_num = config["path_num"] ? config["path_num"].as<int>() : m_param.path_num;
            m_param.thread_priority = config["thread_priority"] ? config["thread_priority"].as<int>() : m_param.thread_priority;
            m_param.dist_jitter_thresh = config["dist_jitter_thresh"] ? config["dist_jitter_thresh"].as<float>() : m_param.dist_jitter_thresh;
            m_param.max_speckle_size_ = m_param.img_width*m_param.img_width*m_param.speckle_ratio_;
            const cv::Mat left_cam_mat = cv::Mat(cam_mat_l.rows(), cam_mat_l.cols(), CV_64FC1, cam_mat_l.data()).t();
            const cv::Mat right_cam_mat = cv::Mat(cam_mat_r.rows(), cam_mat_r.cols(), CV_64FC1, cam_mat_r.data()).t();
            const cv::Mat left_cam_dist = cv::Mat(cam_dist_l.rows(), cam_dist_l.cols(), CV_64FC1, cam_dist_l.data());
            const cv::Mat right_cam_dist = cv::Mat(cam_dist_r.rows(), cam_dist_r.cols(), CV_64FC1, cam_dist_r.data());
            Eigen::Matrix3d rot_tmp = m_param.T_leftcam_rightcam_.block<3, 3>(0, 0);
            const cv::Mat rot_left_right = cv::Mat(3, 3, CV_64FC1, rot_tmp.data()).t();
            const cv::Mat trans_left_right(3, 1, CV_64FC1, m_param.T_leftcam_rightcam_.block<3, 1>(0, 3).data());

            const cv::Size img_size = cv::Size(m_param.img_width, m_param.img_height);//图像大小
            //计算双目校正所用矩阵
            cv::Mat RL,RR,PL,PR,Q;//左相机校正旋转矩阵，右相机校正旋转矩阵，左相机校正平移矩阵，右相机校正平移矩阵，Q深度映射矩阵
            cv::stereoRectify(left_cam_mat,
                left_cam_dist,
                right_cam_mat,
                right_cam_dist,
                img_size,
                rot_left_right,
                trans_left_right,
                RL,RR,
                PL,PR,
                Q, cv::CALIB_ZERO_DISPARITY);
            cv::Mat R_1_to_2 = RR.t()*RL;
            m_param.focal_length_ = PL.at<double>(0, 0);
            m_param.baseline_ = cv::norm(trans_left_right); //*1.0544; //*1.042;
            m_param.doffs_ = Q.at<double>(3,3) * m_param.baseline_;

            //Eigen::Matrix3d K_l;
            for(int v=0; v<3; v++)
            {
                for(int u=0; u<3; u++)
                {
                    m_param.leftCamK_(v,u) = PL.at<double>(v,u);
                    m_param.rightCamK_(v,u) = PR.at<double>(v,u);
                }
            }
            m_param.leftCamD_.setZero();
            m_param.rightCamD_.setZero();

            // std::cout << "m_param.leftCamK_ " <<m_param.leftCamK_<< std::endl;
            // std::cout << "m_param.rightCamK_ " <<m_param.rightCamK_<< std::endl;
            // std::cout << "m_param.leftCamD_ " <<m_param.leftCamD_<< std::endl;
            // std::cout << "m_param.rightCamD_ " <<m_param.rightCamD_<< std::endl;

            // std::cout << "m_param.focal_length_ " <<m_param.focal_length_<< std::endl;
            // std::cout << "m_param.baseline_ " <<m_param.baseline_<< std::endl;
            // std::cout << "m_param.doffs_ " <<m_param.doffs_<< std::endl;

            //畸变校正            
            initUndistortRectifyMap(left_cam_mat, left_cam_dist, RL, PL, img_size, CV_32FC1, undist_map1l, undist_map2l );
            initUndistortRectifyMap(right_cam_mat, right_cam_dist, RR, PR, img_size, CV_32FC1, undist_map1r, undist_map2r );

            const cv::Mat Rotate_L_to_recL = RL;
            const cv::Mat Trans_L_to_recL = (cv::Mat_<double>(3, 1) << 0., 0., 0.);
            const cv::Mat Rotate_R_to_recR = RR;
            const cv::Mat Trans_R_to_recR = (cv::Mat_<double>(3, 1) << 0., 0., 0.);

            const cv::Mat Rotate_recL_to_L = RL.t();
            const cv::Mat Trans_recL_to_L = -RL.t() * Trans_L_to_recL;
            const cv::Mat Rotate_recR_to_R = RR.t();
            const cv::Mat Trans_recR_to_R = -RR.t() * Trans_R_to_recR;

            Eigen::Matrix4d T_recL2L = Eigen::Matrix4d::Identity();
            for(int v=0; v<3; v++)
            {
                for(int u=0; u<3; u++)
                {
                    T_recL2L(v,u) = Rotate_recL_to_L.at<double>(v,u);
                }
            }
            for(int i=0; i<3; i++)
            {
                T_recL2L(i,3) = Trans_recL_to_L.at<double>(i,0);
            }

            Eigen::Matrix3d rot_tmp_lidar = m_param.T_lidar_leftcam_.block<3, 3>(0, 0);
            const cv::Mat Rotate_lidar_to_L = cv::Mat(rot_tmp_lidar.rows(), rot_tmp_lidar.cols(), CV_64FC1, rot_tmp_lidar.data()).t();
            const cv::Mat Trans_lidar_to_L(3, 1, CV_64FC1, m_param.T_lidar_leftcam_.block<3, 1>(0, 3).data());
            Eigen::Matrix4d T_lidar2cam = Eigen::Matrix4d::Identity();
            for(int v=0; v<3; v++)
            {
                for(int u=0; u<3; u++)
                {
                    T_lidar2cam(v,u) = Rotate_lidar_to_L.at<double>(v,u);
                }
            }
            for(int i=0; i<3; i++)
            {
                T_lidar2cam(i,3) = Trans_lidar_to_L.at<double>(i,0);
            }

            const cv::Mat Rotate_lidar_to_recL = RL * Rotate_lidar_to_L;
            const cv::Mat Trans_lidar_to_recL = RL * Trans_lidar_to_L;
            //Eigen::Matrix4d T_lidar2recL = Eigen::Matrix4d::Identity();
            for(int v=0; v<3; v++)
            {
                for(int u=0; u<3; u++)
                {
                    m_param.T_lidar_leftcam_(v,u) = Rotate_lidar_to_recL.at<double>(v,u);
                }
            }
            for(int i=0; i<3; i++)
            {
                m_param.T_lidar_leftcam_(i,3) = Trans_lidar_to_recL.at<double>(i,0);
            }

            m_param.running_mode = listereo::RUNNING_MODE::RUNNING_IN_CPU;
            #if ALG_RUNNING_MODE == 1
            m_param.running_mode = listereo::RUNNING_MODE::RUNNING_IN_GPU_CUDA;
            #endif 

            #if ALG_RUNNING_MODE == 2
            m_param.running_mode = listereo::RUNNING_MODE::RUNNING_IN_GPU_OCL;
            #endif 

            if(m_param.running_mode == listereo::RUNNING_MODE::RUNNING_IN_GPU_OCL
                || m_param.running_mode == listereo::RUNNING_MODE::RUNNING_IN_GPU_CUDA){
                m_param.p1_ = 10;
                m_param.p2_ = 120;
                m_param.uniqueness_ratio_ = 95;
                m_param.sub_shift_ = 6;
            }

            m_sgm_cpu = std::make_unique<StereoSGMCPU>(m_param);
            #if ALG_RUNNING_MODE == 1
            m_sgm_cuda = std::make_unique<StereoSGMCuda>(m_param, undist_map1l, undist_map2l, undist_map1r, undist_map2r, sizeof(ColorPoint3D));
            #elif ALG_RUNNING_MODE == 2
            int platform_idx = m_param.platform_idx;
            int device_idx = m_param.device_idx;
            cl_uint num_platform;
            clGetPlatformIDs(0, nullptr, &num_platform);
            assert((size_t)platform_idx < num_platform);
            std::vector<cl_platform_id> platform_ids(num_platform);
            clGetPlatformIDs(num_platform, platform_ids.data(), nullptr);
            if(platform_ids.size() <= platform_idx)
            {
                std::cout << "Wrong platform index!" << std::endl;
                exit(0);
            }
            cl_int err;
            std::cout << "Number of platforms: " << num_platform << std::endl;
            std::cout << "Target platform id: " << platform_idx << std::endl;
            
            // 尝试查找GPU/CPU平台，输出平台信息
            int device_type = CL_DEVICE_TYPE_CPU;
            char platformName[128];
            clGetPlatformInfo(platform_ids[platform_idx], CL_PLATFORM_NAME, sizeof(platformName), platformName, NULL);
            std::cout << "Found platform: " << platformName << std::endl;

            cl_uint numDevices;
            err = clGetDeviceIDs(platform_ids[platform_idx], CL_DEVICE_TYPE_CPU, 0, NULL, &numDevices);
            if (err == CL_SUCCESS && numDevices > 0) {
                std::cout << "  Platform has " << numDevices << " CPU device(s)." << std::endl;
            } 
            else {
                err = clGetDeviceIDs(platform_ids[platform_idx], CL_DEVICE_TYPE_GPU, 0, NULL, &numDevices);
                std::cout << "  Platform has " << numDevices << " GPU device(s)." << std::endl;
                device_type = CL_DEVICE_TYPE_GPU;
            }

            cl_uint num_devices;
            clGetDeviceIDs(platform_ids[platform_idx], device_type, 0, nullptr, &num_devices);
            assert((size_t)device_idx < num_devices);
            std::vector<cl_device_id> cl_devices(num_devices);
            clGetDeviceIDs(platform_ids[platform_idx], device_type, num_devices, cl_devices.data(), nullptr);
            m_cl_device = cl_devices[device_idx];
            m_cl_ctx = clCreateContext(nullptr, 1, &cl_devices[device_idx], context_error_callback, NULL, &err);

            size_t max_work_group_size;
            clGetDeviceInfo(m_cl_device, CL_DEVICE_MAX_WORK_GROUP_SIZE, sizeof(size_t), &max_work_group_size, NULL);
            std::cout << "  Device max_work_group_size " << max_work_group_size << std::endl;

            cl_ulong global_mem_size;
            clGetDeviceInfo(m_cl_device, CL_DEVICE_GLOBAL_MEM_SIZE, sizeof(cl_ulong), &global_mem_size, NULL);
            std::cout << "  Device global_mem_size " << global_mem_size << std::endl;

            cl_ulong local_mem_size;
            clGetDeviceInfo(m_cl_device, CL_DEVICE_LOCAL_MEM_SIZE, sizeof(cl_ulong), &local_mem_size, NULL);
            std::cout << "  Device local_mem_size " << local_mem_size << std::endl;

            cl_uint compute_units;
            clGetDeviceInfo(m_cl_device, CL_DEVICE_MAX_COMPUTE_UNITS, sizeof(cl_uint), &compute_units, NULL);

            std::cout << "  Device compute_units: " << compute_units << std::endl;

            if (err == CL_SUCCESS) {
                
                size_t name_size_in_bytes;
                clGetPlatformInfo(platform_ids[platform_idx], CL_PLATFORM_NAME, 0, nullptr, &name_size_in_bytes);
                std::string platform_name;
                platform_name.resize(name_size_in_bytes);
                clGetPlatformInfo(platform_ids[platform_idx], CL_PLATFORM_NAME,
                    platform_name.size(),
                    (void*)platform_name.data(), nullptr);
                std::cout << "Run in platform: " << platform_name << std::endl;

                clGetDeviceInfo(cl_devices[device_idx], CL_DEVICE_NAME, 0, nullptr, &name_size_in_bytes);
                std::string dev_name;
                dev_name.resize(name_size_in_bytes);
                clGetDeviceInfo(cl_devices[device_idx], CL_DEVICE_NAME,
                    dev_name.size(),
                    (void*)dev_name.data(), nullptr);
                std::cout << "Run in device: " << dev_name << std::endl;

            } else {
                std::cout << "Error creating context " << err << std::endl;
                throw std::runtime_error("Error creating context!");
            }
            
            m_sgm_ocl = std::make_unique<StereoSGMOpenCL>(
                m_cl_ctx,  
                m_cl_device,  
                m_param, 
                m_param.leftCamK_, 
                (float)m_param.baseline_, 
                (float)m_param.focal_length_, 
                (float)m_param.doffs_);
            m_cl_queue_ = clCreateCommandQueue(m_cl_ctx, m_cl_device, 0, &err); 
            #endif
            std::cout << "DepthEstimationImpl: Configuration parameters loaded successfully." << std::endl;

        } catch (const YAML::Exception& e) {
            std::cerr << "DepthEstimationImpl: Error - Failed to read config parameters: " << e.what() << std::endl;
            return false;
        }  

        m_is_initialized = true;
        
        try {
            m_worker_thread = std::thread(&DepthEstimationImpl::workerLoop, this);

            // 获取线程句柄
            pthread_t handle = m_worker_thread.native_handle();

            // 设置调度策略和优先级
            int policy = SCHED_FIFO; // 可以是 SCHED_FIFO, SCHED_RR, or SCHED_OTHER
            struct sched_param thread_param;
            
            // 获取当前策略的优先级范围
            int max_priority = sched_get_priority_max(policy);
            int min_priority = sched_get_priority_min(policy);
            std::cout << "DepthEstimationImpl: Max priority for SCHED_FIFO: " << max_priority << std::endl;
            std::cout << "DepthEstimationImpl: Min priority for SCHED_FIFO: " << min_priority << std::endl;

            // 设置优先级，1-99，越高优先级越高
            thread_param.sched_priority = std::min(max_priority, std::max(min_priority, m_param.thread_priority));

            // 设置线程的调度参数
            if (pthread_setschedparam(handle, policy, &thread_param) != 0) {
                std::cerr << "DepthEstimationImpl: Failed to set thread priority!" << std::endl;
            } else {
                std::cout << "DepthEstimationImpl: Successfully set thread priority to " << thread_param.sched_priority << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "DepthEstimationImpl: Failed to start worker thread: " << e.what() << std::endl;
            m_is_initialized = false;
            return false;
        }
        std::cout << "DepthEstimationImpl: Initialization complete and worker thread started." << std::endl;
        return m_is_initialized;
    }
    /**
     * @brief 设置结果回调函数。
     * @param callback 回调函数的函数指针。
     * @return true 设置成功；false 设置失败。
     */
    bool setResultsCallback(DepthEstimationCallback callback){
        if (!callback) {
            std::cerr << "DepthEstimationImpl: Error - Callback function is null." << std::endl;
            return false;
        }

        std::cout << "DepthEstimationImpl: Callback function set successfully." << std::endl;
        m_callback = callback;
        return true;
    }

    /**
     * @brief 接收Lidar和双目图像数据并触发算法处理。
     * @param lidar_data Lidar数据。
     * @param image_data 双目图像数据。
     */
    void onDataReceived(const LidarData& lidar_data, const StereoImage& image_data){
        if (!m_is_initialized) {
            std::cerr << "DepthEstimationImpl: Error - Cannot process data. Not initialized." << std::endl;
            return;
        }

        if (!m_callback) {
            std::cerr << "DepthEstimationImpl: Error - No callback function set. Results will not be returned." << std::endl;
            return;
        }

        // std::cout << "DepthEstimationImpl: Data received, processing..." << std::endl;

        // 数据有效性检查
        if (lidar_data.size == 0 || !lidar_data.buffer) {
            std::cerr << "DepthEstimationImpl: Error - Invalid Lidar data." << std::endl;
            return;
        }
        if (image_data.img_width == 0 || image_data.img_height == 0 || !image_data.left_img_buffer || !image_data.right_img_buffer) {
            std::cerr << "DepthEstimationImpl: Error - Invalid Stereo image data." << std::endl;
            return;
        }
        // 数据时间戳检查
        double time_diff = std::abs(static_cast<double>(lidar_data.timestamp) - static_cast<double>(image_data.timestamp)) * 1e-6;
        if (time_diff > 100) { // 100ms
            std::cerr << "DepthEstimationImpl: Warning - Large time difference between Lidar and Image data: " << time_diff << "ms" << std::endl;
            return;
        }
        {
            const size_t MAX_QUEUE_SIZE = 10; // 设置最大队列大小
            std::lock_guard<std::mutex> lock(m_queue_mutex);        
            // 如果队列已满，丢弃最旧的数据
            if (m_data_queue.size() >= MAX_QUEUE_SIZE) {
                m_data_queue.pop(); // 丢弃队列头部数据
                std::cerr << "DepthEstimationImpl: Warning - Queue is full, dropping oldest frame." << std::endl;
            }

            m_data_queue.push({lidar_data, image_data});
            m_condition.notify_one(); 
        }
        return;
    }

private:
    std::thread m_worker_thread;
    std::queue<SensorDataPacket> m_data_queue;
    std::mutex m_queue_mutex;
    std::condition_variable m_condition;
    bool m_stop_thread = false;
    bool m_is_initialized = false;

    cv::Mat undist_map1l,undist_map2l,undist_map1r,undist_map2r;//左相机输出1/2映射，右相机输出1/2映射

    std::string m_config_path;
    DepthEstimationCallback m_callback = nullptr;
    LiSGMParam m_param;
    std::unique_ptr<StereoSGMCPU> m_sgm_cpu;
    #if ALG_RUNNING_MODE == 1
    std::unique_ptr<StereoSGMCuda> m_sgm_cuda;
    #elif ALG_RUNNING_MODE == 2
    std::unique_ptr<StereoSGMOpenCL> m_sgm_ocl;
    cl_context m_cl_ctx = nullptr;
    cl_device_id m_cl_device = nullptr;
    cl_command_queue m_cl_queue_ = nullptr; 
    #endif

    // 算法的核心处理函数，在 onDataReceived 中被调用
    void runAlgorithm(SensorDataPacket& sensor_data_packet) {
        // --- 算法核心逻辑模拟 ---
        // std::cout << "DepthEstimationImpl: Algorithm running on a separate thread , img_timestamp: " << sensor_data_packet.image_data.timestamp << std::endl;
        auto results = std::make_shared<DepthEstimationResults>();
        results->lidar_data = sensor_data_packet.lidar_data;
        results->stereo_image = sensor_data_packet.image_data;
        
        DepthImage& depth_img = results->depth_image;
        depth_img.img_width = results->stereo_image.img_width;
        depth_img.img_height = results->stereo_image.img_height;
        depth_img.bits_size = 32;
        depth_img.timestamp = results->stereo_image.timestamp;

        // 分配新内存并填充深度数据。        
        if(!depth_img.depth_buffer) {
            size_t depth_buffer_size = depth_img.img_width * depth_img.img_height * (depth_img.bits_size / 8);
            depth_img.depth_buffer = std::make_shared<std::vector<uint8_t>>(depth_buffer_size, 0);
        }
        const auto t0 = std::chrono::system_clock::now();
        LidarData & lidar_data = results->lidar_data;
        std::vector<Eigen::Vector3f> prior_p3d;
        for(size_t i = 0; i < lidar_data.size / sizeof(pcl::PointXYZ); ++i) {
            if (i >= lidar_data.size / sizeof(pcl::PointXYZ)) {
                break; // 防止越界
            }
            pcl::PointXYZ* pt = reinterpret_cast<pcl::PointXYZ*>(lidar_data.buffer->data() + i * sizeof(pcl::PointXYZ));
            if (std::isfinite(pt->x) && std::isfinite(pt->y) && std::isfinite(pt->z)) {
                Eigen::Vector4d p(pt->x, pt->y, pt->z, 1.0);
                Eigen::Vector4d transformed_p = m_param.T_lidar_leftcam_ * p;
                prior_p3d.emplace_back(Eigen::Vector3f(transformed_p(0),transformed_p(1),transformed_p(2)));
                // std::cout << "P" << i << " " << transformed_p.transpose() << std::endl;
            }
        }
        const auto t1 = std::chrono::system_clock::now();
        cv::Mat depth_img_mat;
        if(m_param.enable_vision == 1){
            depth_img_mat = cv::Mat(depth_img.img_height, depth_img.img_width, CV_16UC1, depth_img.depth_buffer->data(), depth_img.img_width * sizeof(uint16_t));
        } else {
            depth_img_mat = cv::Mat(depth_img.img_height, depth_img.img_width, CV_32FC1, depth_img.depth_buffer->data(), depth_img.img_width * sizeof(float));
        }
        
        cv::Mat left_img_mat = cv::Mat(results->stereo_image.img_height, results->stereo_image.img_width, CV_8UC3, results->stereo_image.left_img_buffer->data(), results->stereo_image.img_width * 3 * sizeof(char));
        cv::Mat right_img_mat = cv::Mat(results->stereo_image.img_height, results->stereo_image.img_width, CV_8UC3, results->stereo_image.right_img_buffer->data(), results->stereo_image.img_width * 3 * sizeof(char));
        // cv::Mat rectify_left, rectify_right;
        // cv::remap(left_img_mat, rectify_left, undist_map1l, undist_map2l, cv::INTER_LINEAR);
        // cv::remap(right_img_mat, rectify_right, undist_map1r, undist_map2r, cv::INTER_LINEAR);
        const auto t2 = std::chrono::system_clock::now();
        // cv::imwrite("rectify_left.png",rectify_left);
        // cv::imwrite("rectify_right.png",rectify_right);
        // cv::Mat output_img;
        // cv::hconcat(rectify_left, rectify_right, output_img);

        // // 在校正后的图像上画线，以验证像素行是否对齐
        // for (int i = 0; i < output_img.rows; i += 20) {
        //     cv::line(output_img, cv::Point(0, i), cv::Point(output_img.cols, i), cv::Scalar(0, 255, 0));
        // }

        // cv::imshow("Rectified Stereo Images", output_img);
        // cv::waitKey(0);
        #if ALG_RUNNING_MODE == 1        
        if (!m_sgm_cuda) {
            std::cerr << "DepthEstimationImpl: Error - SGM CUDA not initialized." << std::endl;
            return;
        }
        std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> triangles;
        std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> uv_p3d;
        cv::Mat idx_map;
        m_sgm_cpu->computeTriangles(triangles, uv_p3d, idx_map, prior_p3d, m_param.leftCamK_, m_param.img_height, m_param.img_width, m_param);
        const auto t2_1 = std::chrono::system_clock::now();
        // std::cout << "DepthEstimationImpl: Running SGM algorithm... " << std::endl;
        m_sgm_cuda->run_lidar(uv_p3d, triangles, idx_map, prior_p3d);
        const auto t3 = std::chrono::system_clock::now();
        // m_sgm_cuda->get_cuda_image(depth_semi, listereo::CudaImageType::PRIOR_DEPTH);
        m_sgm_cuda->run_vision(left_img_mat.data, right_img_mat.data);
        if(m_param.enable_vision == 1){
            m_sgm_cuda->get_cuda_image(depth_img_mat, listereo::CudaImageType::RET_DEPTH);  
        }            
        else {
            m_sgm_cuda->get_cuda_image(depth_img_mat, listereo::CudaImageType::LIDAR_DENSE_DEPTH);            
        }
        results->color_point_cloud.point_step = sizeof(ColorPoint3D);
        results->color_point_cloud.timestamp = depth_img.timestamp;
        m_sgm_cuda->get_cuda_data(results->color_point_cloud.point_cloud_buffer, &(results->color_point_cloud.point_num)); 
        m_sgm_cuda->get_cuda_image(left_img_mat, listereo::CudaImageType::UNDISTORT_IMAGE_LEFT); 
        m_sgm_cuda->get_cuda_image(right_img_mat, listereo::CudaImageType::UNDISTORT_IMAGE_RIGHT); 
        const auto t4 = std::chrono::system_clock::now();
        
        // debug 统计耗时
        const auto d02 = std::chrono::duration_cast<std::chrono::microseconds>(t2 - t0).count();
        const auto d21 = std::chrono::duration_cast<std::chrono::microseconds>(t2_1 - t2).count();
        const auto d23 = std::chrono::duration_cast<std::chrono::microseconds>(t3 - t2_1).count();
        const auto d34 = std::chrono::duration_cast<std::chrono::microseconds>(t4 - t3).count();
        const auto d = std::chrono::duration_cast<std::chrono::milliseconds>(t4 - t0).count();
        float fps = 1000.0f / d;
            // std::cout << std::setw(8) << "DepthEstimationImpl: Run algorithm cost: " << (float)d << " ms, FPS: " << std::setw(8) << fps << std::endl; 
        // 打印耗时
        // 标题行
        std::cout << std::left 
                    << std::setw(8) << "Steps:" 
                    << std::setw(15) << "PreProcess" 
                    << std::setw(15) << "RunLidarCPU" 
                    << std::setw(15) << "RunLidarGPU" 
                    << std::setw(15) << "RunVision" 
                    << std::setw(15) << "FPS" 
                    << std::endl;
          // 数据行
          std::cout << std::setw(8) << "Costs:" 
                    << std::setw(15) << (float)d02 / 1000.0f 
                    << std::setw(15) << (float)d21 / 1000.0f 
                    << std::setw(15) << (float)d23 / 1000.0f 
                    << std::setw(15) << (float)d34 / 1000.0f
                    << std::setw(15) << fps
                    << std::endl;
         

        #elif ALG_RUNNING_MODE == 2
        if (!m_sgm_ocl) {
            std::cerr << "DepthEstimationImpl: Error - SGM OpenCL not initialized." << std::endl;
            return;
        }
        cv::Mat img_left = cv::Mat(results->stereo_image.img_height, results->stereo_image.img_width, CV_8UC3, results->stereo_image.left_img_buffer->data(), results->stereo_image.img_width * sizeof(uint8_t) * 3);
        cv::Mat img_right = cv::Mat(results->stereo_image.img_height, results->stereo_image.img_width, CV_8UC3, results->stereo_image.right_img_buffer->data(), results->stereo_image.img_width * sizeof(uint8_t) * 3);
        
        cv::Mat gray_l, gray_r;
        if (results->stereo_image.img_channel == 3) {
            cv::cvtColor(img_left, gray_l, cv::COLOR_BGR2GRAY);
            cv::cvtColor(img_right, gray_r, cv::COLOR_BGR2GRAY);
        } else {
            gray_l = img_left;
            gray_r = img_right;
        }
        std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> triangles;
        std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> uv_p3d;
        cv::Mat idx_map;
        m_sgm_cpu->computeTriangles(triangles, uv_p3d, idx_map, prior_p3d, m_param.leftCamK_, m_param.img_height, m_param.img_width, m_param);
        m_sgm_ocl->run_lidar(uv_p3d, triangles, idx_map, m_cl_queue_);
        m_sgm_ocl->run_vision(gray_l, gray_r, m_cl_queue_);
        m_sgm_ocl->get_ocl_image(depth_img_mat, listereo::OCLImageType::RET_DEPTH, m_cl_queue_);   
        #else
        if (!m_sgm_cpu) {
            std::cerr << "DepthEstimationImpl: Error - SGM CPU not initialized." << std::endl;
            return;
        }
        cv::Mat img_left = cv::Mat(results->stereo_image.img_height, results->stereo_image.img_width, CV_8UC3, results->stereo_image.left_img_buffer->data(), results->stereo_image.img_width * sizeof(uint8_t) * 3);
        cv::Mat img_right = cv::Mat(results->stereo_image.img_height, results->stereo_image.img_width, CV_8UC3, results->stereo_image.right_img_buffer->data(), results->stereo_image.img_width * sizeof(uint8_t) * 3);
        
        cv::Mat gray_l, gray_r;
        if (results->stereo_image.img_channel == 3) {
            cv::cvtColor(img_left, gray_l, cv::COLOR_BGR2GRAY);
            cv::cvtColor(img_right, gray_r, cv::COLOR_BGR2GRAY);
        } else {
            gray_l = img_left;
            gray_r = img_right;
        }
        cv::Mat depth_vis, depth_semi;
        m_sgm_cpu->run(depth_img_mat, depth_vis, depth_semi, gray_l, gray_r, prior_p3d);
        #endif
        // 调用回调函数返回结果
        m_callback(results);
        //std::cout << "DepthEstimationImpl: Algorithm finished. Results are ready." << std::endl;
    }

    void workerLoop() {
        while (!m_stop_thread && m_is_initialized) {
            SensorDataPacket sensor_data_packet;
            
            {
                std::unique_lock<std::mutex> lock(m_queue_mutex);
                m_condition.wait(lock, [this]{
                    return !m_data_queue.empty() || m_stop_thread;
                });
                
                if (m_stop_thread && m_data_queue.empty()) {
                    break;
                }

                sensor_data_packet = m_data_queue.front();
                m_data_queue.pop();
            } 
            const auto t0 = std::chrono::system_clock::now();
            runAlgorithm(sensor_data_packet);
            const auto t1 = std::chrono::system_clock::now();
            const auto d = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
            float fps = 1000.0f / d;
            // std::cout << std::setw(8) << "DepthEstimationImpl: Run algorithm cost: " << (float)d << " ms, FPS: " << std::setw(8) << fps << std::endl; 
        }
    }

    void stopWorker() {
        //只有当线程是 joinable 状态时才执行 join
        if (m_worker_thread.joinable()) {
            {
                std::lock_guard<std::mutex> lock(m_queue_mutex);
                m_stop_thread = true;
                m_is_initialized = false;
            }
            m_condition.notify_one(); 
            m_worker_thread.join();
        }
    }

};
// --- 工厂函数的实现 ---
// 这是在 DepthEstimationInterface.h 中声明的工厂函数的具体实现。
// 创建 DepthEstimationImpl 的实例，并将其作为基类指针返回。
std::unique_ptr<DepthEstimationInterface> createDepthEstimator() {
    return std::make_unique<DepthEstimationImpl>();
}
} // namespace listereo
} // namespace robosense