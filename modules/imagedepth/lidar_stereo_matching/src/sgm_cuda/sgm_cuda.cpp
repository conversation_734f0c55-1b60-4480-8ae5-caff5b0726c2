#include "sgm_cuda/sgm_cuda.h"
#include <cuda_runtime.h> 
#include <fstream>
namespace robosense {
namespace listereo{
    StereoSGMCuda::StereoSGMCuda(
      const listereo::LiSGMParam &params, 
      const cv::Mat &map_x_l, 
      const cv::Mat &map_y_l, 
      const cv::Mat &map_x_r, 
      const cv::Mat &map_y_r, 
      const int color_point_type_size):
    m_params_(params),
    mi_color_point_type_size_(color_point_type_size)
    {
        // 预设图像参数
        int width = params.img_width;
        int height = params.img_height;
        int depth_type = params.depth_type;
        mi_src_pitch_ = width;
        mi_dst_pitch_ = width;
        

        mf_ks_left_[0] = 1.f/params.leftCamK_(0,0);
        mf_ks_left_[1] = 1.f/params.leftCamK_(1,1); 
        mf_ks_left_[2] = -params.leftCamK_(0,2)/params.leftCamK_(0,0);
        mf_ks_left_[3] = -params.leftCamK_(1,2)/params.leftCamK_(1,1);

        mf_ks_right_[0] = 1.f/params.rightCamK_(0,0);
        mf_ks_right_[1] = 1.f/params.rightCamK_(1,1); 
        mf_ks_right_[2] = -params.rightCamK_(0,2)/params.rightCamK_(0,0);
        mf_ks_right_[3] = -params.rightCamK_(1,2)/params.rightCamK_(1,1);

        for(int i = 0; i < 8; i++){
            mf_dist_left_[i] = params.leftCamD_(i);
            mf_dist_right_[i] = params.rightCamD_(i);
        }

        mf_thres_cos_ = std::cos(params.thres_angle_/180.f*M_PI);

        for(int i = 0; i < 2; i++){            
            m_d_src_color_[i].create(height, width, SGM_8U3, mi_src_pitch_);
            m_d_src_color_undist_[i].create(height, width, SGM_8U3, mi_src_pitch_);
            m_d_map_x_[i].create(height, width, SGM_32F, mi_src_pitch_);
            m_d_map_y_[i].create(height, width, SGM_32F, mi_src_pitch_);
            if(m_params_.enable_vision ==1){
                m_d_src_gray_[i].create(height, width, SGM_8U, mi_src_pitch_);
                m_d_src_gray_undist_[i].create(height, width, SGM_8U, mi_src_pitch_);
                m_d_sobel_gx_[i].create(height, width, SGM_8U, mi_src_pitch_);
                const ImageType census_type = SGM_32U;
                m_d_census_[i].create(height, width, census_type);
                m_d_gx_census_[i].create(height, width, census_type);
                m_d_tmp_[i].create(height, width, SGM_16U, mi_dst_pitch_);
                m_d_disp_[i].create(height, width, SGM_16U, mi_dst_pitch_);       
            }                 
        }
        m_d_depth_.create(height, width, SGM_32F, mi_dst_pitch_);
        m_d_cost_.create(params.path_num, height * width * params.num_disp_, SGM_8U);
        m_d_prior_disp_.create(height, width, SGM_32F, mi_dst_pitch_);

        m_d_ks_[0].create(4, DATA_32F);
        m_d_ks_[0].upload(mf_ks_left_);
        m_d_ks_[1].create(4, DATA_32F);
        m_d_ks_[1].upload(mf_ks_right_);

        m_d_dist_[0].create(8, DATA_32F);
        m_d_dist_[0].upload(mf_dist_left_);
        m_d_dist_[1].create(8, DATA_32F);
        m_d_dist_[1].upload(mf_dist_right_);

        m_d_sparse_depth_.create(height, width, SGM_32F, mi_dst_pitch_);
        m_d_dense_depth_.create(height, width, SGM_32F, mi_dst_pitch_);

        m_d_map_x_[0].upload(map_x_l.data);
        m_d_map_x_[1].upload(map_x_r.data);
        m_d_map_y_[0].upload(map_y_l.data);
        m_d_map_y_[1].upload(map_y_r.data);
        m_d_T_.create(16, DATA_32F);
        Eigen::Matrix4f T_lidar_leftcam = params.T_lidar_leftcam_.cast<float>();
        m_d_T_.upload(T_lidar_leftcam.data());
        std::cout << "T_lidar_leftcam: \n" << T_lidar_leftcam << std::endl;
        m_d_color_point3d_.create(height * width, DATA_CUSTOM, color_point_type_size);
    }

    int StereoSGMCuda::run_lidar(
        const std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> &uv_p3d, 
        const std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &triangles, 
        const cv::Mat &idx_mat,
        const std::vector<Eigen::Vector3f> &points3d)
    {
        const auto t0 = std::chrono::system_clock::now();
        if(uv_p3d.empty() || triangles.empty() || idx_mat.empty()){
            std::cerr << "Input data is empty!" << std::endl;
            return -1;
        }
        // 将内存数据写入cuda设备内存
        int point_num = uv_p3d.size();
        int triangle_num = triangles.size();        
        
        // 创建cuda device缓冲区
        m_d_uv_p3d_.create(point_num * 5, DATA_32F);
        m_d_triangles_.create(triangle_num * 6, DATA_32S);
        m_d_idx_mat_.create(idx_mat.rows, idx_mat.cols, SGM_32U);
        
        m_d_normals_.create(point_num * 3, DATA_32S);
        m_d_normals_fixed_.create(point_num * 3, DATA_32F);
        m_d_normals_count_.create(point_num, DATA_32S);
        
        // 将数据上传到GPU
        // std::cout << "Uploading data to GPU..." << std::endl;
        m_d_uv_p3d_.upload(uv_p3d.data());
        m_d_triangles_.upload(triangles.data());
        m_d_idx_mat_.upload(idx_mat.data);

        // 计算先验深度图
        int err = 0;
        err |= ComputePriorDepthCuda(point_num, triangle_num);
        //矫正法向
        err |= ComputeAverageNormalsCuda(point_num);        
        //过滤坏点
        err |= FilterBadPriorDepthCuda(point_num);

        // int point_num_3d = points3d.size();
        // m_d_point3d_.create(point_num_3d * 3, DATA_32F);
        // m_d_point3d_.upload(points3d.data());
        // err = ProjectPCL2Img(point_num_3d);
        // err |= GetDensePriorDepth();  

        if(m_params_.enable_vision == 1){
            // 深度图转换为视差图
            err |= Depth2DispCuda();
        }
        err |= cudaDeviceSynchronize();
        const auto t1 = std::chrono::system_clock::now();

        // debug 统计耗时
        const auto d_lidar = std::chrono::duration_cast<std::chrono::microseconds>(t1 - t0).count();

        // // 打印耗时
        // std::cout << std::left 
        //             << std::setw(8) << "Lidar Cost: " << (float)d_lidar / 1000.0f
        //             << std::endl;
        return err;
    }

    int StereoSGMCuda::run_vision(
      uchar* left_buffer, 
      uchar* right_buffer,
      int channel)
    {
        if(left_buffer==nullptr || right_buffer==nullptr){
            std::cerr << "Input images image are empty!" << std::endl;
            return -1;
        }
        const auto t0 = std::chrono::system_clock::now();
        int err = 0;
        if(channel == 3){
            m_d_src_color_[0].upload(left_buffer);
            m_d_src_color_[1].upload(right_buffer);
        }
        else if(channel == 1){
            m_d_src_gray_[0].upload(left_buffer);
            m_d_src_gray_[1].upload(right_buffer);
        }
        else{
            std::cerr << "Unsupported image channel: " << channel << std::endl;
            return -1;
        }

        err |= ImageUndistort();
        if(m_params_.enable_vision == 1){           
        
            // 转换为灰度图
            err |= CvtColor2Gray();

            err |= ComputeSobelCuda();
            for(int i = 0; i < 4; i++){
                err |= ComputeCensusCuda(i);
            }
            const auto t1 = std::chrono::system_clock::now();
            
            err |= ComputeVisionCostAndAggCuda();
            const auto t2 = std::chrono::system_clock::now();
            
            err |= ComputeDisparityFromAggCuda();

            // err |= cudaDeviceSynchronize();

            const auto t3 = std::chrono::system_clock::now();

            err |= LRConsistencyCheckCuda();

            err |= MedianFilterCuda();

            err |= Disp2DepthCuda();
  
            const auto d01 = std::chrono::duration_cast<std::chrono::microseconds>(t1 - t0).count();
            const auto d12 = std::chrono::duration_cast<std::chrono::microseconds>(t2 - t1).count();
            const auto d23 = std::chrono::duration_cast<std::chrono::microseconds>(t3 - t2).count();
            // debug 统计耗时
            const auto t4 = std::chrono::system_clock::now();        
            const auto d34 = std::chrono::duration_cast<std::chrono::microseconds>(t4 - t3).count();
            const auto d_vision = std::chrono::duration_cast<std::chrono::microseconds>(t4 - t0).count();
        }
        // err |= cudaDeviceSynchronize(); 
        mi_valid_points_count = 0;        
        err |= GetColorPointCloud(&mi_valid_points_count);   

        // 执行同步
        err |= cudaDeviceSynchronize(); 
        // // 打印耗时
        // // 标题行
        // std::cout << std::left 
        //             << std::setw(8) << "Steps:" 
        //             << std::setw(15) << "PreProcess" 
        //             << std::setw(15) << "CensusAggCost" 
        //             << std::setw(15) << "DispFromAgg" 
        //             << std::setw(15) << "PoseProcess"
        //             << std::endl;
        //   // 数据行
        //   std::cout << std::setw(8) << "Costs:" 
        //             << std::setw(15) << (float)d01 / 1000.0f 
        //             << std::setw(15) << (float)d12 / 1000.0f 
        //             << std::setw(15) << (float)d23 / 1000.0f
        //             << std::setw(15) << (float)d34 / 1000.0f
        //             << std::endl;
        return err;
    }

    int StereoSGMCuda::CvtColor2Gray()
    {
        int err = cuda_kernels_api::color2gray(m_d_src_color_undist_[0], m_d_src_color_undist_[1], m_d_src_gray_[0], m_d_src_gray_[1]);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_8UC1);
        // m_d_src_gray_[0].download(debug.data);
        // cv::imshow("gray", debug);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::ImageUndistort()
    {
        int err = 0;
        err |= cuda_kernels_api::remap(m_d_src_color_undist_[0],m_d_src_color_[0], m_d_map_x_[0], m_d_map_y_[0]);
        err |= cuda_kernels_api::remap(m_d_src_color_undist_[1],m_d_src_color_[1], m_d_map_x_[1], m_d_map_y_[1]);
        // cv::Mat debug_raw(m_params_.img_height, m_params_.img_width, CV_8UC3);
        // m_d_src_color_[1].download(debug_raw.data);
        // cv::Mat debug_undist(m_params_.img_height, m_params_.img_width, CV_8UC3);
        // m_d_src_color_undist_[1].download(debug_undist.data);
        // cv::imshow("debug_raw", debug_raw);
        // cv::imshow("debug_undist", debug_undist);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::ComputeCensusCuda(int img_idx)
    {
        int err = 0;
        if(img_idx < 2){
            err |= cuda_kernels_api::census_transform(m_d_src_gray_[img_idx], m_d_census_[img_idx]);
            // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32SC1);
            // m_d_census_[img_idx].download(debug.data);
            // cv::Mat debug_8u, debug_color;
            // debug.convertTo(debug_8u, CV_8U, 0.00000001);
            // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
            // cv::imshow("census", debug_8u);
            // cv::waitKey(0);
        }            
        else if(img_idx < 4){
            err |= cuda_kernels_api::census_transform(m_d_sobel_gx_[img_idx - 2], m_d_gx_census_[img_idx - 2]);
            // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32FC1);
            // m_d_gx_census_[img_idx - 2].download(debug.data);
            // cv::Mat debug_8u, debug_color;
            // debug.convertTo(debug_8u, CV_8U, 255);
            // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
            // cv::imshow("gx census", debug_8u);
            // cv::waitKey(0);
        }            
        
        return err;
    }

    int StereoSGMCuda::ComputeSobelCuda()
    {
        int err = cuda_kernels_api::sobel_gx_feature(m_d_src_gray_[0], m_d_src_gray_[1], m_d_sobel_gx_[0], m_d_sobel_gx_[1]);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_8UC1);
        // m_d_sobel_gx_[1].download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 1);
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("sobel", debug_8u);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::ComputeVisionCostAndAggCuda()
    {
        int err = cuda_kernels_api::cost_aggregation(m_d_census_[0], m_d_census_[1], m_d_cost_, m_params_.p1_, m_params_.p2_, m_params_.path_num, m_params_.min_disp_, m_params_.num_disp_);
        return err;
    }

    int StereoSGMCuda::ComputeDisparityFromAggCuda()
    {
        const float uniqueness_ratio = m_params_.uniqueness_ratio_ * 0.01;
        const uint16_t weight_shift = m_params_.weight_shift_;
        const uint16_t den = 1<<weight_shift;
        uint16_t a = std::min(m_params_.weight_depth_, den);
        uint16_t b = den-a;

        float alpha = m_params_.enable_lidar == 1 ? (float)(a) / float(den) : 0.f;
        float beta = m_params_.enable_lidar == 1 ? (float)(b) / float(den) : 1.f;
        int err = cuda_kernels_api::disp_from_agg(
            m_d_cost_, 
            m_d_prior_disp_, 
            m_d_tmp_[0], 
            m_d_tmp_[1], 
            uniqueness_ratio,
            m_params_.min_disp_,
            m_params_.num_disp_, 
            m_params_.path_num, 
            m_params_.sub_shift_,
            m_params_.r_pd_,
            m_params_.pd1_,
            m_params_.pd2_,
            alpha,
            beta);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_16UC1);
		// m_d_tmp_[0].download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 0.01);
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("disp", debug_8u);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::LRConsistencyCheckCuda()
    {
        int err = cuda_kernels_api::check_consistency(m_d_tmp_[0], m_d_tmp_[1], m_d_src_gray_[0], m_params_.min_disp_, m_params_.disp_lr_max_diff_, m_params_.sub_shift_);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_16UC1);
		// m_d_tmp_[0].download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 0.001);
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("consistency disp", debug_8u);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::MedianFilterCuda()
    {
        int err = cuda_kernels_api::median_filter(m_d_tmp_[0], m_d_disp_[0]);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_16UC1);
		// m_d_disp_[0].download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 1.0 / 256);
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("median filtered disp", debug_8u);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::Disp2DepthCuda()
    {
        int err = cuda_kernels_api::disp2depth(
            m_d_disp_[0], 
            m_d_dense_depth_,
            m_d_depth_,            
            m_params_.baseline_, 
            m_params_.focal_length_, 
            m_params_.doffs_, 
            1.0f / (1 << m_params_.sub_shift_),
            m_params_.enable_lidar,
            m_params_.semi_rel_error_,
            m_params_.thres_cross_check_);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_16UC1);
        // m_d_depth_.download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 255.0f * 0.001 );
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("depth from disp", debug_color);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::Depth2DispCuda()
    {
        int err = cuda_kernels_api::depth2disp(
            m_d_dense_depth_, 
            m_d_prior_disp_, 
            m_params_.baseline_, 
            m_params_.focal_length_, 
            m_params_.doffs_, 
            1.0f / (1 << m_params_.sub_shift_));
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32FC1);
        // m_d_prior_disp_.download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 1.0f);
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("disp from depth", debug_8u);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::ComputePriorDepthCuda(int point_num, int triangle_num)
    {
        int err = 0;
        // m_d_dense_depth_.fill_max();
        err |= cuda_kernels_api::triangle2depth(
            m_d_uv_p3d_, 
            m_d_triangles_, 
            m_d_idx_mat_, 
            m_d_dense_depth_, 
            m_d_normals_, 
            m_d_normals_count_,
            point_num, 
            triangle_num, 
            mf_thres_cos_, 
            m_d_ks_[0]);        
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32FC1);
        // m_d_dense_depth_.download(debug.data);
        // cv::Mat debug_8u, debug_mat;
        // debug.convertTo(debug_8u, CV_8UC1, 1.f);
        // cv::imshow("prior depth", debug_8u);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::ComputeAverageNormalsCuda(int point_num)
    {
        int err = cuda_kernels_api::rectify_normals(
            m_d_normals_, 
            m_d_normals_count_, 
            m_d_normals_fixed_, 
            point_num);
        return err;
    }

    int StereoSGMCuda::FilterBadPriorDepthCuda(int point_num)
    {
        int err = cuda_kernels_api::filter_bad_prior_depth(
            m_d_dense_depth_, 
            m_d_uv_p3d_,
            m_d_normals_fixed_, 
            m_d_idx_mat_, 
            point_num, 
            m_params_.r_prior_, 
            m_d_ks_[0]);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32FC1);
        // m_d_dense_depth_.download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 256 * 0.001f);
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("filtered prior depth", debug_8u);
        // cv::waitKey(0);
        return err;
    }   

    int StereoSGMCuda::ProjectPCL2Img(int points_num)
    {
        //std::cout << "points_num " << points_num << std::endl;
        // m_d_sparse_depth_.fill_zero();
        int err = cuda_kernels_api::project_pcl2img(m_d_point3d_, m_d_sparse_depth_, points_num, m_d_ks_[0]);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32FC1);
        // m_d_sparse_depth_.download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 1000.f);
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("sparse depth", debug_8u);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::GetDensePriorDepth()
    {
        m_d_dense_depth_.fill_zero();
        int err = cuda_kernels_api::get_dense_depth(m_d_sparse_depth_, m_d_dense_depth_, m_params_.r_prior_, m_params_.dist_jitter_thresh, m_d_ks_[0]);
        // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32FC1);
        // m_d_dense_depth_.download(debug.data);
        // cv::Mat debug_8u, debug_color;
        // debug.convertTo(debug_8u, CV_8U, 256 * 0.001f);
        // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
        // cv::imshow("dense depth", debug_8u);
        // cv::waitKey(0);
        return err;
    }

    int StereoSGMCuda::GetColorPointCloud(int *valid_point_count)
    {
        int *d_valid_point_count;

        // 在设备端分配内存
        int err = cudaMalloc((void**)&d_valid_point_count, sizeof(int));
        err |= cudaMemcpy(d_valid_point_count, valid_point_count, sizeof(int), cudaMemcpyHostToDevice);
        if (err != cudaSuccess) {
            return err;
        }
        if(m_params_.enable_vision == 1)
            err |= cuda_kernels_api::get_color_pointcloud(m_d_depth_, m_d_src_color_undist_[0], m_d_color_point3d_, m_d_ks_[0], m_d_T_, d_valid_point_count);
        else 
            err |= cuda_kernels_api::get_color_pointcloud(m_d_dense_depth_, m_d_src_color_undist_[0], m_d_color_point3d_, m_d_ks_[0], m_d_T_, d_valid_point_count);

        err |= cudaMemcpy(valid_point_count, d_valid_point_count, sizeof(int), cudaMemcpyDeviceToHost);

        // 释放设备端内存
        cudaFree(d_valid_point_count);
        return err;
    }
    
    int StereoSGMCuda::get_cuda_image(cv::Mat &ret, CudaImageType image_type)
    {
        if(ret.empty()){
            std::cerr << "Output image is empty!" << std::endl;
            return -1;
        }
        const auto t0 = std::chrono::system_clock::now();
        switch (image_type)
        {
        case CudaImageType::RET_DISP:
            m_d_disp_[0].download(ret.data);
            break;
        case CudaImageType::RET_DEPTH:
            m_d_depth_.download(ret.data);
            break;
        case CudaImageType::PRIOR_DISP:
            m_d_prior_disp_.download(ret.data);
            break;  
        case CudaImageType::LIDAR_DENSE_DEPTH:
            m_d_dense_depth_.download(ret.data);
            break;
        case CudaImageType::UNDISTORT_IMAGE_LEFT:
            m_d_src_color_undist_[0].download(ret.data);
            break; 
        case CudaImageType::UNDISTORT_IMAGE_RIGHT:
            m_d_src_color_undist_[1].download(ret.data);
            break;           
        default:
            break;
        }
        const auto t1 = std::chrono::system_clock::now();
        const auto d_get_results = std::chrono::duration_cast<std::chrono::microseconds>(t1 - t0).count();
        // std::cout << std::left 
        //     << std::setw(8) << "GetResults " << (int)image_type << " Cost: " << (float)d_get_results / 1000.0f
        //     << std::endl;
        return 0;
    }

    int StereoSGMCuda::get_cuda_data(std::shared_ptr<std::vector<uint8_t>> &host_data, uint32_t *valid_point_num)
    {
        *valid_point_num = mi_valid_points_count;
        host_data = std::make_shared<std::vector<uint8_t>>(mi_valid_points_count * mi_color_point_type_size_, 0);
        m_d_color_point3d_.download(host_data->data(), mi_color_point_type_size_, mi_valid_points_count);
        return 0;
    }
} // namespace listereo
} // namespace robosense