#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#include <math.h>
#include <stdint.h>
#include <device_atomic_functions.h>

#define DEPTH_FIXED_SCALE 65536.0f // 根据实际情况设置

namespace robosense {
namespace listereo
{

namespace cuda_kernels_api
{

    // 点云数据结构
    struct PointXYZRGBA_device {
        float x;
        float y;
        float z;
        uint8_t r;
        uint8_t g;
        uint8_t b;
        uint8_t a;
    };
        // 核函数：将三维点投影到图像平面
    __global__ void get_color_pointcloud_kernel(
        PointXYZRGBA_device* color_points3d,            // 三维点云数据 (X, Y, Z, R, G, B, A)
        const float* dense_depth,                       // 深度图像数据
        const uint8_t* color_image,                     // 彩色图像数据        
        const float* K,                                 // 相机内参矩阵
        const float* T,                                 // 相机和Lidar的矩阵
        const int img_width,                            // 图像宽度
        const int img_height,                           // 图像高度
        int *valid_points_count
        ) 
    {
        // 从相机内参矩阵K中读取参数
        float fx = 1.f / K[0];
        float fy = 1.f / K[1];
        float cx = -fx * K[2];
        float cy = -fy * K[3];

        int u = blockIdx.x * blockDim.x + threadIdx.x;
        int v = blockIdx.y * blockDim.y + threadIdx.y;

        int depth_pixel_idx = v * img_width + u;
        int color_pixel_idx = depth_pixel_idx * 3;

        if (u >= img_width || v >= img_height) {
            return;
        }
        // if(u == 0 && v == 0){
        //     printf("R00, R01, R02 T0: %f, %f, %f, %f\n", T[0], T[4], T[8], T[12]);
        //     printf("R10, R11, R12 T1: %f, %f, %f, %f\n", T[1], T[5], T[9], T[13]);
        //     printf("R20, R21, R22 T2: %f, %f, %f, %f\n", T[2], T[6], T[10], T[14]);
        // }

        // 获取深度值
        float d = dense_depth[depth_pixel_idx];

        // 检查深度是否有效
        if (d <= 0) {
            return;
        }

        // 计算3D坐标        
        float x = (u - cx) * d / fx;
        float y = (v - cy) * d / fy;
        float z = d;

        float lidar_x = T[0] * x + T[4] * y + T[8] * z + T[12];
        float lidar_y = T[1] * x + T[5] * y + T[9] * z + T[13];
        float lidar_z = T[2] * x + T[6] * y + T[10] * z  + T[14];

        uint8_t b = color_image[color_pixel_idx + 0];
        uint8_t g = color_image[color_pixel_idx + 1];
        uint8_t r = color_image[color_pixel_idx + 2];

        PointXYZRGBA_device color_pt;
        color_pt.x = lidar_x;
        color_pt.y = lidar_y;
        color_pt.z = lidar_z;
        color_pt.r = r;
        color_pt.g = g;
        color_pt.b = b;
        color_pt.a = 255;

        // 使用原子操作获取写入索引
        int index = atomicAdd(valid_points_count, 1);
        // 将点写入点云数组
        color_points3d[index] = color_pt;
        
    }

    int get_color_pointcloud(
        const DeviceImage& dense_depth,
        const DeviceImage& color_image,
        DeviceData& color_points3d, 
        const DeviceData& K,
        const DeviceData& T,
        int *valid_points_count
    )
    {
        cudaError_t err = cudaSuccess;  
        const int img_width = dense_depth.cols;
        const int img_height = dense_depth.rows;
        
        const int w_per_block = 16;
        const int h_per_block = 16;
        const dim3 bdim(w_per_block, h_per_block);
        const dim3 gdim(divUp(img_width, w_per_block), divUp(img_height, h_per_block));

        get_color_pointcloud_kernel<<<gdim, bdim>>>((PointXYZRGBA_device*)color_points3d.ptr<uint8_t>(), dense_depth.ptr<float>(), color_image.ptr<uint8_t>(), K.ptr<float>(), T.ptr<float>(), img_width, img_height, valid_points_count);

        // 检查错误
        err = cudaGetLastError();
        if (err != cudaSuccess) {
            printf("Failed to launch get_color_pointcloud_kernel (error code %s)!\n", cudaGetErrorString(err));
            return -1;
        }

        return err;
    }
}
} // namespace listereo
} // namespace robosense


