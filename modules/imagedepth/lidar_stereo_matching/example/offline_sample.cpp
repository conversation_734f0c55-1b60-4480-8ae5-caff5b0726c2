#include "depth_estimation_interface.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <pcl/point_types.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/common/transforms.h>
#include <pcl/visualization/pcl_visualizer.h>

typedef pcl::PointXYZ PointType;
typedef pcl::PointCloud<PointType> PointCloudXYZI;
typedef PointCloudXYZI::Ptr CloudPtr;

// 外部定义的回调函数
void result_handler(const std::shared_ptr<robosense::listereo::DepthEstimationResults>& results) {
    if (results) {
        // 检查数据有效性
        if (!results->depth_image.depth_buffer || 
            !results->stereo_image.left_img_buffer ||
            !results->stereo_image.right_img_buffer ||
            !results->color_point_cloud.point_cloud_buffer || 
            results->depth_image.depth_buffer->empty() || 
            results->stereo_image.left_img_buffer->empty() || 
            results->stereo_image.right_img_buffer->empty() ||
            results->color_point_cloud.point_cloud_buffer->empty()
            ) {
            std::cout << "Callback Error: Result buffer is NULL or empty." << std::endl;
            return;
        }
        if(results->depth_image.bits_size != 32 && results->depth_image.bits_size != 16){
            std::cout << "Callback Error: Invalid depth type. [" << results->depth_image.bits_size << "]" << std::endl;
            return;
        }
        std::cout << "Callback received results! depth_timestamp: " << results->depth_image.timestamp << std::endl;

        //获取深度图格式
        int depth_type = results->depth_image.bits_size == 32 ? CV_32FC1 : CV_16UC1;
        int pixel_size = results->depth_image.bits_size / 8;
        //将深度数据转换为cv::Mat
        cv::Mat depth_img_mat = cv::Mat(results->depth_image.img_height, results->depth_image.img_width, depth_type, results->depth_image.depth_buffer->data(), results->depth_image.img_width * pixel_size);
        //获取彩色图数据（无畸变），并转换为cv::Mat
        cv::Mat left_img_mat = cv::Mat(results->stereo_image.img_height, results->stereo_image.img_width, CV_8UC3, results->stereo_image.left_img_buffer->data(), results->stereo_image.img_width * 3 * sizeof(uint8_t));
        //获取彩色点云，将其转换为PCL点云格式
        robosense::listereo::ColorPoint3D *color_point_cloud_ptr = (robosense::listereo::ColorPoint3D *)(results->color_point_cloud.point_cloud_buffer->data());
        pcl::PointCloud<pcl::PointXYZRGBA>::Ptr pcl_point_cloud_ptr(new pcl::PointCloud<pcl::PointXYZRGBA>);
        pcl_point_cloud_ptr->points.reserve(results->color_point_cloud.point_num);
        for(int i = 0; i < results->color_point_cloud.point_num; i++){
            
            robosense::listereo::ColorPoint3D color_pt = color_point_cloud_ptr[i];            
            pcl::PointXYZRGBA pcl_pt;
            pcl_pt.x = color_pt.x;
            pcl_pt.y = color_pt.y;
            pcl_pt.z = color_pt.z;

            pcl_pt.r = color_pt.r;
            pcl_pt.g = color_pt.g;
            pcl_pt.b = color_pt.b;
            pcl_pt.a = color_pt.a;
            pcl_point_cloud_ptr->points.push_back(pcl_pt);
        }

        // draw xyz axis into point cloud
        // for(int i = 0; i < 100; i++){            
        //     pcl::PointXYZRGBA pcl_pt;
        //     pcl_pt.x = 0.1 * float(i);
        //     pcl_pt.y = 0;
        //     pcl_pt.z = 0;
        //     pcl_pt.r = 255;
        //     pcl_pt.g = 0;
        //     pcl_pt.b = 0;
        //     pcl_pt.a = 255;
        //     pcl_point_cloud_ptr->points.push_back(pcl_pt);
        // }

        // for(int i = 0; i < 100; i++){            
        //     pcl::PointXYZRGBA pcl_pt;
        //     pcl_pt.x = 0;
        //     pcl_pt.y = 0.1 * float(i);
        //     pcl_pt.z = 0;
        //     pcl_pt.r = 0;
        //     pcl_pt.g = 255;
        //     pcl_pt.b = 0;
        //     pcl_pt.a = 255;
        //     pcl_point_cloud_ptr->points.push_back(pcl_pt);
        // }

        // for(int i = 0; i < 100; i++){            
        //     pcl::PointXYZRGBA pcl_pt;
        //     pcl_pt.x = 0;
        //     pcl_pt.y = 0;
        //     pcl_pt.z = 0.1 * float(i);
        //     pcl_pt.r = 0;
        //     pcl_pt.g = 0;
        //     pcl_pt.b = 255;
        //     pcl_pt.a = 255;
        //     pcl_point_cloud_ptr->points.push_back(pcl_pt);
        // }


        // // 显示点云
        // // 创建一个可视化窗口
        // pcl::visualization::PCLVisualizer::Ptr viewer(new pcl::visualization::PCLVisualizer("PCL Viewer"));
        // viewer->setBackgroundColor(0, 0, 0); // 设置背景为黑色

        // // 将点云添加到可视化器中
        // viewer->addPointCloud<pcl::PointXYZRGBA>(pcl_point_cloud_ptr, "sample cloud");
        // viewer->setPointCloudRenderingProperties(pcl::visualization::PCL_VISUALIZER_POINT_SIZE, 1, "sample cloud");

        // // 保持窗口打开
        // while (!viewer->wasStopped()) {
        //     viewer->spinOnce(1000);
        // }
        
        // 显示图像
        cv::Mat depth_8u, depth_color;
        depth_img_mat.convertTo(depth_8u, CV_8UC1, 256);
        cv::applyColorMap(depth_8u, depth_color, cv::COLORMAP_JET);
        cv::Mat mask = (depth_8u == 0); // 无效深度掩码
        depth_color.setTo(cv::Scalar(0, 0, 0), mask); // BGR黑色
        cv::imshow("Color Left Image", left_img_mat);
        cv::imshow("Depth Image", depth_color);      
        char key = cv::waitKey(1);

        if(key == 's'){
            // 将深度图数据保存为二进制文件
            std::ofstream ofs("depth_float.bin", std::ios::out | std::ios::binary);
            if (!ofs.is_open()) {
                std::cerr << "Error: Could not open depth_float.bin " << std::endl;
                return;
            }
            ofs.write(reinterpret_cast<const char*>(depth_img_mat.data), results->depth_image.img_height * results->depth_image.img_width * sizeof(float));
            ofs.close();

            // 将稠密点云保存为pcd文件
            pcl::io::savePCDFile("color_point_cloud.pcd", *pcl_point_cloud_ptr, true);

            // 将稠密点云数据保存为二进制文件
            std::ofstream ofs_pcl_bin("color_point_cloud.bin", std::ios::out | std::ios::binary);
            if (!ofs_pcl_bin.is_open()) {
                std::cerr << "Error: Could not open color_point_cloud.bin " << std::endl;
                return;
            }
            // 保存点云结构体的元数据
            ofs_pcl_bin.write(reinterpret_cast<const char*>(&results->color_point_cloud.point_num), sizeof(results->color_point_cloud.point_num));
            ofs_pcl_bin.write(reinterpret_cast<const char*>(&results->color_point_cloud.point_step), sizeof(results->color_point_cloud.point_step));
            ofs_pcl_bin.write(reinterpret_cast<const char*>(&results->color_point_cloud.timestamp), sizeof(results->color_point_cloud.timestamp));
            // 保存点云二进制数据，先写入整个缓冲区的大小，再写入数据
            size_t buffer_size = results->color_point_cloud.point_cloud_buffer->size();
            ofs_pcl_bin.write(reinterpret_cast<const char*>(&buffer_size), sizeof(buffer_size));
            ofs_pcl_bin.write(reinterpret_cast<const char*>(results->color_point_cloud.point_cloud_buffer->data()), buffer_size);
            ofs_pcl_bin.close();
        }
    }
}

int main(int argc, char* argv[]) {

    if(argc < 3)
    {
        std::cout << "Usage: " << argv[0] << " <data_path> <config_path>" << std::endl;
        return -1;
    }    

    // 创建算法实例
    std::unique_ptr<robosense::listereo::DepthEstimationInterface> de_algorithm = robosense::listereo::createDepthEstimator();

    if (!de_algorithm) {
        std::cerr << "Failed to create depth estimator." << std::endl;
        return -1;
    }

    // 1. 初始化
    const std::string config_path = argv[2];
    if (!de_algorithm->initialize(config_path + "/config.yaml")) {
        std::cerr << "Initialization failed!" << std::endl;
        return -1;
    }

    // 2. 设置回调函数
    de_algorithm->setResultsCallback(result_handler);
    
    while(true){
        // 读取测试数据
        const std::string data_path = argv[1];

        cv::Mat image_left = cv::imread(data_path + "/im0.png", cv::IMREAD_UNCHANGED);
        cv::Mat image_right = cv::imread(data_path + "/im1.png", cv::IMREAD_UNCHANGED);
        
        CloudPtr cloud_prior(new PointCloudXYZI);
        if(pcl::io::loadPCDFile<PointType>(data_path + "/lidar_prior.pcd", *cloud_prior) == -1) 
        {
            PCL_ERROR("无法读取文件\n");
            return -1;
        }
        // 3. 模拟数据接收
        robosense::listereo::LidarData lidar_data;
        robosense::listereo::StereoImage image_data;
        // 填充Lidar数据
        size_t lidar_buffer_size = cloud_prior->size() * sizeof(PointType);
        lidar_data.buffer = std::make_shared<std::vector<uint8_t>>(lidar_buffer_size);
        std::memcpy(lidar_data.buffer->data(), cloud_prior->points.data(), lidar_buffer_size);
        lidar_data.size = static_cast<unsigned int>(lidar_buffer_size);
        lidar_data.timestamp = 1234567890; // 示例时间戳
        // 填充图像数据
        size_t left_image_size = image_left.total() * image_left.elemSize();
        size_t right_image_size = image_right.total() * image_right.elemSize();
        image_data.left_img_buffer = std::make_shared<std::vector<uint8_t>>(left_image_size);
        image_data.right_img_buffer = std::make_shared<std::vector<uint8_t>>(right_image_size);
        std::memcpy(image_data.left_img_buffer->data(), image_left.data, left_image_size);
        std::memcpy(image_data.right_img_buffer->data(), image_right.data, right_image_size);
        image_data.img_channel = image_left.channels();
        image_data.img_width = image_left.cols;
        image_data.img_height = image_left.rows;
        image_data.timestamp = 1234567890; // 示例时间戳
        
        // 调用算法处理数据
        de_algorithm->onDataReceived(lidar_data, image_data);
        std::this_thread::sleep_for(std::chrono::milliseconds(66)); // 模拟数据间隔
    }    
    return 0;
}
